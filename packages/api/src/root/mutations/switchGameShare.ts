import { TRPCError } from "@trpc/server"
import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { db } from "../db"
import { communitySchema } from "../db/schema/community.schema"
import { userToCommunitySchema } from "../db/schema/userToCommunity.schema"
import { usersSchema } from "../db/schema/users.schema"
import { OK_RESPONSE } from "../helpers/responses"
import { hasPermission } from "../permissions"
import { protectedProcedure } from "../trpc/procedures/protectedProcedure"

export const switchGameShare = protectedProcedure
  .input(
    z.object({
      share: z.boolean(),
      communityId: z.number(),
      userId: z.number(),
    }),
  )
  .mutation(async ({ input, ctx: { loginData } }) => {
    const communityCheck = await db
      .select({
        id: communitySchema.id,
        openness: communitySchema.openness,
        share: communitySchema.allowShare,
      })
      .from(communitySchema)
      .where(eq(communitySchema.id, input.communityId))
      .then((community) => community[0])

    if (!communityCheck) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "This community does not exist",
      })
    }

    if (
      !hasPermission(loginData, "community", "shareUserGames", {
        id: input.communityId,
        viewedUserId: input.userId,
        allowShare: communityCheck.share,
      })
    ) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You don't have permission to change this setting",
      })
    }

    const userCheck = await db
      .select({
        id: usersSchema.id,
      })
      .from(usersSchema)
      .where(eq(usersSchema.id, input.userId))
      .then((user) => user[0])

    if (!userCheck) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "This user does not exist",
      })
    }

    db.update(userToCommunitySchema)
      .set({
        shareMyGames: input.share,
      })
      .where(
        and(
          eq(userToCommunitySchema.userId, input.userId),
          eq(userToCommunitySchema.communityId, input.communityId),
        ),
      )
      .then((re) => re)

    return OK_RESPONSE
  })
