import { TRPCError } from "@trpc/server"
import { eq } from "drizzle-orm"
import { z } from "zod"

import { Role } from "../../../../common/src/permissions/roles/helpers/types"
import { db } from "../db"
import { communityToEventSchema } from "../db/schema/communityToEvent.schema"
import { eventSchema } from "../db/schema/event.schema"
import {
  removeEventRole,
  setAsInterested,
  setAsParticipant,
  setAsRequested,
  setAsReserved,
} from "../helpers/insertEventRoleAndCount"
import { OK_RESPONSE } from "../helpers/responses"
import { protectedProcedure } from "../trpc/procedures/protectedProcedure"

export const changeMyEventStatus = protectedProcedure
  .input(
    z.object({
      eventId: z.number(),
      status: z.enum([
        "participant",
        "interested",
        "requested",
        "reserved",
        "notgoing",
      ]),
    }),
  )
  .mutation(async ({ input: { eventId, status }, ctx: { loginData } }) => {
    const event = await db
      .select({
        id: eventSchema.id,
        openness: eventSchema.openness,
        status: eventSchema.state,
        maxCapacity: eventSchema.maxCapacity,
        reserveCapacity: eventSchema.reserveCapacity,
        going: eventSchema.going,
        reserve: eventSchema.reserve,
        memberApproval: eventSchema.memberApproval,
      })
      .from(eventSchema)
      .where(eq(eventSchema.id, eventId))
      .then(async (event) => {
        if (!event[0]) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Not Found: Such event does not exist",
          })
        }

        const hosts = await db
          .select({
            id: communityToEventSchema.communityId,
            owner: communityToEventSchema.owner,
          })
          .from(communityToEventSchema)
          .where(eq(communityToEventSchema.eventId, event[0].id))
          .then((communities) => communities)

        return {
          hosts,
          ...event[0],
        }
      })

    if (!event) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Not Found: : Such event does not exist",
      })
    }

    if (event.status !== "open" && event.status !== "ongoing") {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "Forbidden: Event is not open for participation",
      })
    }

    let requestResult: Role | null = null

    try {
      switch (status) {
        case "requested":
          requestResult = await setAsRequested({ user: loginData, event })
          break
        case "reserved":
          requestResult = await setAsReserved({ user: loginData, event })
          break
        case "participant":
          requestResult = await setAsParticipant({ user: loginData, event })
          break
        case "interested":
          requestResult = await setAsInterested({ user: loginData, event })
          break
        case "notgoing":
        default:
          requestResult = await removeEventRole({ user: loginData, event })
          break
      }
    } catch (error: unknown) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: error as string,
      })
    }

    return { ...OK_RESPONSE, role: requestResult }
  })
