import { cancelInviteCommunity } from "./mutations/cancelInviteCommunity"
import { changeMyEventStatus } from "./mutations/changeMyEventStatus"
import { changeUserEventStatus } from "./mutations/changeUserEventStatus"
import { changeUserStatus } from "./mutations/changeUserStatus"
import { configureCommunity } from "./mutations/configureCommunity"
import { configureEvent } from "./mutations/configureEvent"
import { confirmCommunityEventInvite } from "./mutations/confirmCommunityEventInvite"
import { createCommunity } from "./mutations/createCommunity"
import { createEvent } from "./mutations/createEvent"
import { createInvite } from "./mutations/createInvite"
import { createPrivateEvent } from "./mutations/createPrivateEvent"
import { declineCommunityEventInvite } from "./mutations/declineCommunityEventInvite"
import { inviteCommunity } from "./mutations/inviteCommunity"
import { joinByInvite } from "./mutations/joinByInvite"
import { joinCommunity } from "./mutations/joinCommunity"
import { leaveCommunity } from "./mutations/leaveCommunity"
import { switchGameShare } from "./mutations/switchGameShare"
import { userProfileUpdate } from "./mutations/userProfileUpdate"
import { communityBasic } from "./queries/community/communityBasic"
// import { deprecatedCommunityEventsList } from "./queries/community/communityEventsList"
import { communityExpanded2 } from "./queries/community/communityExpanded2"
import { communityGameExtended2 } from "./queries/community/communityGameExtended2"
import { communityGamesList2 } from "./queries/community/communityGamesList2"
import { communityInvites } from "./queries/community/communityInvites"
import { communityList } from "./queries/community/communityList"
import { communityUserInfo } from "./queries/community/communityUserInfo"
import { communityUserList } from "./queries/community/communityUserList"
import { joinByInvitePrejoin } from "./queries/community/joinByInvitePrejoin"
import { publicCommunityList } from "./queries/community/publicCommunityList"
import { eventBasic } from "./queries/event/eventBasic"
import { eventExtended } from "./queries/event/eventExtended"
import { eventGamesList } from "./queries/event/eventGamesList"
import { eventOrganizers } from "./queries/event/eventOrganizers"
import { eventParticipant } from "./queries/event/eventParticipant"
import { eventParticipantList } from "./queries/event/eventParticipantList"
import { eventsList } from "./queries/event/eventsList"
import { getItemInfo } from "./queries/game/getItemInfo"
import { getMyGameInfo } from "./queries/game/getMyGameInfo"
import { searchCommunities } from "./queries/search/searchCommunities"
import { tagList } from "./queries/tagList"
import { getMyInfo } from "./queries/user/getMyInfo"
import { loggedInUser } from "./queries/user/loggedInUser"
import { logoutUser } from "./queries/user/logoutUser"
import { router } from "./trpc/trpc"

export const appRouter = router({
  logoutUser,
  communityList,
  loggedInUser,
  communityExpanded2,
  communityGamesList2,
  communityBasic,
  communityGameExtended2,
  communityUserList,
  communityUserInfo,
  leaveCommunity,
  userProfileUpdate,
  getMyInfo,
  publicCommunityList,
  joinCommunity,
  joinByInvite,
  changeUserStatus,
  createCommunity,
  configureCommunity,
  createInvite,
  switchGameShare,
  getMyGameInfo,
  tagList,
  joinByInvitePrejoin,
  createEvent,
  createPrivateEvent,
  eventExtended,
  inviteCommunity,
  cancelInviteCommunity,
  eventBasic,
  getItemInfo,
  // communityEventsList: deprecatedCommunityEventsList,
  eventParticipantList,
  communityInvites,
  configureEvent,
  changeUserEventStatus,
  eventOrganizers,
  confirmCommunityEventInvite,
  eventGamesList,
  eventsList,
  changeMyEventStatus,
  eventParticipant,
  declineCommunityEventInvite,
  searchCommunities,
}) // ...

export type AppRouter = typeof appRouter
