import { Autocomplete, Box, Button, TextField, Typo<PERSON> } from "@mui/material"
import { useRouter } from "@tanstack/react-router"
import debounce from "debounce"
import { useCallback, useEffect, useMemo, useState } from "react"

import { SEARCH_STR_LENGTH } from "../../../config/game.conf"
import { createImageLink } from "../../../config/images"
import { useModalStore } from "../../../store/useModalStore"
import { trpc } from "../../../trpc/trpc"
import { Modal } from "../Modal"

import * as styles from "./inviteCommunityModal.module.css"

export const INVITE_COMMUNITY_MODAL_NAME = "inviteCommunity"

type InviteCommunityModalProps = {
  eventId: number
}

type Community = {
  id: number
  name: string
  image: string
  openness: string
}

export const InviteCommunityModal = ({
  eventId,
}: InviteCommunityModalProps) => {
  const [search, setSearch] = useState("")
  const [selected, setSelected] = useState<Community | null>(null)
  const [communities, setCommunities] = useState<Community[]>([])
  const router = useRouter()
  const { closeModal } = useModalStore()

  const onSearch = (search: string) => {
    setSearch(search)
  }

  const onSearchDelayed = useMemo(() => debounce(onSearch, 300), [onSearch])

  const handleInviteCommunity = useCallback(async () => {
    if (!selected) {
      return
    }
    try {
      await trpc.inviteCommunity.mutate({
        eventId,
        communityId: selected.id,
      })
      router.invalidate()
    } catch (error) {
      console.error(error)
    } finally {
      closeModal(INVITE_COMMUNITY_MODAL_NAME)
    }
  }, [selected, eventId])

  const handleSelectOption = useCallback(
    (option: Community | null) => {
      if (!option) {
        return
      }
      setSelected(option)
    },
    [setSelected],
  )

  useEffect(() => {
    if (search.length < SEARCH_STR_LENGTH) {
      return
    }
    trpc.searchCommunities.query({ search }).then((data) => {
      setCommunities(data)
    })
  }, [search])

  return (
    <Modal
      name={INVITE_COMMUNITY_MODAL_NAME}
      title="Invite Community as organizer"
    >
      <Box m={1} display="flex" flexDirection="column" gap={2} minWidth="300px">
        <Autocomplete
          options={communities}
          getOptionKey={(value) => value.id.toString()}
          getOptionLabel={(value) => value.name}
          renderInput={(params) => <TextField {...params} label="Search" />}
          onInputChange={(_, value) => onSearchDelayed(value)}
          onChange={(_, value) => handleSelectOption(value)}
        />

        {selected && (
          <Box>
            <Box
              display="flex"
              flexDirection="row"
              gap={2}
              alignItems="center"
              m={2}
            >
              <Box
                borderRadius="8px"
                overflow="hidden"
                maxWidth="100px"
                maxHeight="100px"
              >
                <img
                  className={styles.img}
                  src={createImageLink(
                    "community",
                    "small",
                    selected.id,
                    selected.image,
                  )}
                  alt={selected.name}
                />
              </Box>
              <Box>
                <Typography variant="h6">{selected.name}</Typography>
                <Typography
                  variant="body2"
                  color="textSecondary"
                  textTransform="uppercase"
                >
                  {selected.openness}
                </Typography>
              </Box>
            </Box>
            <Box m={2}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleInviteCommunity}
              >
                Invite
              </Button>
            </Box>
          </Box>
        )}
      </Box>
    </Modal>
  )
}
