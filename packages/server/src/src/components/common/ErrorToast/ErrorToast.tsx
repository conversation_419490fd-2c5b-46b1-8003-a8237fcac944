import { Alert, Snackbar } from "@mui/material"

import { useErrorStore } from "../../../store/useErrorStore"

export const ErrorToast = () => {
  const errors = useErrorStore((state) => state.errors)
  const { markAsDisplayed } = useErrorStore()

  const handleClose = (reason?: string) => {
    if (reason === "clickaway") {
      return
    }
    markAsDisplayed(errors[0])
  }

  if (errors.length === 0) {
    return null
  }

  return (
    <Snackbar
      anchorOrigin={{ vertical: "top", horizontal: "center" }}
      open={errors.length > 0}
      autoHideDuration={1200}
      onClose={(_, reason) => handleClose(reason)}
    >
      <Alert
        onClose={() => handleClose("close")}
        severity="error"
        variant="filled"
        sx={{ width: "100%" }}
      >
        {errors[0].message}
      </Alert>
    </Snackbar>
  )
}
