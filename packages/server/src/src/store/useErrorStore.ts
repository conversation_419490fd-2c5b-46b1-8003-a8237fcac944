import { v4 as uuidv4 } from "uuid"
import { create } from "zustand"

type ErrorMessage = {
  message: string
  code: string
  id: string
}

type UseErrorStoreProps = {
  pastErrors: ErrorMessage[]
  errors: ErrorMessage[]
  setError: (error: Omit<ErrorMessage, "id">) => void
  markAsDisplayed: (error: ErrorMessage) => void
}

export const useErrorStore = create<UseErrorStoreProps>((set) => ({
  errors: [],
  pastErrors: [],
  setError: (error: Omit<ErrorMessage, "id">) =>
    set((state) => ({ errors: [...state.errors, { ...error, id: uuidv4() }] })),
  markAsDisplayed: (error: ErrorMessage) =>
    set((state) => ({
      errors: state.errors.filter((e) => e.id !== error.id),
      pastErrors: [...state.pastErrors, error],
      displayingError: false,
    })),
}))
